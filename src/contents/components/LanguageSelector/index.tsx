import React, { useState } from 'react';
import { getSourceLanguageOptions, getTargetLanguageOptions } from '../../../config/languageConfig';
import * as styles from './index.module.less';
import { useUpdateEffect } from 'ahooks';
import { Select } from 'antd';

export interface LanguageSelection {
  srcLang: string;
  tgtLang: string;
}

interface LanguageSelectorProps {
  onLanguageChange?: (selection: LanguageSelection) => void; // 改为可选参数
  defaultSrcLang?: string;
  defaultTgtLang?: string;
  className?: string;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  onLanguageChange,
  defaultSrcLang = 'auto',
  defaultTgtLang = 'zh',
  className = '',
}) => {
  const [srcLang, setSrcLang] = useState(defaultSrcLang);
  const [tgtLang, setTgtLang] = useState(defaultTgtLang);

  const sourceOptions = getSourceLanguageOptions();
  const targetOptions = getTargetLanguageOptions();

  // 语言变更时通知父组件
  useUpdateEffect(() => {
    console.log('LanguageSelector: Language changed', srcLang, tgtLang);

    // 确保onLanguageChange存在且为函数类型
    if (onLanguageChange && typeof onLanguageChange === 'function') {
      onLanguageChange({ srcLang, tgtLang });
    } else {
      console.warn('LanguageSelector: onLanguageChange is not a valid function', onLanguageChange);
    }
  }, [srcLang, tgtLang]);

  const handleSrcLanguageChange = (value: string) => {
    setSrcLang(value);
  };

  const handleTgtLanguageChange = (value: string) => {
    setTgtLang(value);
  };

  const handleSwapLanguages = () => {
    if (srcLang === 'auto') return; // 自动检测不能作为目标语言

    const newSrcLang = tgtLang;
    const newTgtLang = srcLang;
    setSrcLang(newSrcLang);
    setTgtLang(newTgtLang);
  };

  return (
    <div className={`${styles.languageSelector} ${className}`}>
      <Select value={srcLang} onChange={handleSrcLanguageChange} options={sourceOptions}></Select>
      <div className={styles.arrowIcon}>→</div>
      <Select value={tgtLang} onChange={handleTgtLanguageChange} options={targetOptions}></Select>
    </div>
  );
};

export default LanguageSelector;
